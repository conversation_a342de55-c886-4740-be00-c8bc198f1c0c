inboundCall:
  name: <PERSON>
  description: "Region: NAM 24x7 Team: Wells Fargo"
  division: Home
  startUpRef: "/inboundCall/menus/menu[Main Menu_10]"
  defaultLanguage: en-us
  supportedLanguages:
    en-us:
      defaultLanguageSkill:
        noValue: true
      textToSpeech:
        defaultEngine:
          voice: Jill
  initialGreeting:
    prompt: Prompt.WellsFargoGreeting
  variables:
    - booleanVariable:
        name: Flow.LowPriority
        initialValue:
          lit: false
  settingsActionDefaults:
    playAudioOnSilence:
      timeout:
        lit:
          seconds: 40
    detectSilence:
      timeout:
        lit:
          seconds: 40
    callData:
      processingPrompt:
        noValue: true
    collectInput:
      noEntryTimeout:
        lit:
          seconds: 5
    dialByExtension:
      interDigitTimeout:
        lit:
          seconds: 6
    transferToUser:
      connectTimeout:
        noValue: true
    transferToNumber:
      connectTimeout:
        noValue: true
    transferToGroup:
      connectTimeout:
        noValue: true
    transferToFlowSecure:
      connectTimeout:
        lit:
          seconds: 15
  settingsErrorHandling:
    errorHandling:
      disconnect:
        none: true
    preHandlingAudio:
      tts: Sorry, an error occurred. Please try your call again.
  settingsMenu:
    extensionDialingMaxDelay:
      lit:
        seconds: 1
    listenForExtensionDialing:
      lit: false
    menuSelectionTimeout:
      lit:
        seconds: 10
    repeatCount:
      lit: 3
  settingsPrompts:
    ensureAudioInPrompts: false
    promptMediaToValidate:
      - mediaType: audio
      - mediaType: tts
  settingsSpeechRec:
    completeMatchTimeout:
      lit:
        ms: 100
    incompleteMatchTimeout:
      lit:
        ms: 1500
    maxSpeechLengthTimeout:
      lit:
        seconds: 20
    minConfidenceLevel:
      lit: 50
    asrCompanyDir: none
    asrEnabledOnFlow: true
    suppressRecording: false
  menus:
    - menu:
        name: Main Menu
        refId: Main Menu_10
        audio:
          prompt: Prompt.WellsFargoMainMenu
        settingsMenu:
          extensionDialingMaxDelay:
            noValue: true
          listenForExtensionDialing:
            lit: false
          menuSelectionTimeout:
            noValue: true
          repeatCount:
            lit: 0
        settingsSpeechRec:
          completeMatchTimeout:
            noValue: true
          incompleteMatchTimeout:
            noValue: true
          maxSpeechLengthTimeout:
            noValue: true
          minConfidenceLevel:
            noValue: true
        choices:
          - menuTask:
              name: Repairs and Maintenance
              refId: Repairs and Maintenance_37
              dtmf: digit_2
              globalDtmf: false
              globalSpeechRecTerms: false
              task:
                actions:
                  - decision:
                      name: Decision
                      condition:
                        exp: Call.CalledAddressOriginal=="+***********" or Call.CalledAddressOriginal=="+***********"  or Call.CalledAddressOriginal=="+***********"
                      outputs:
                        "yes":
                          actions:
                            - setParticipantData:
                                name: Set Participant Data
                                attributes:
                                  - attribute:
                                      name:
                                        lit: Report Group
                                      value:
                                        lit: Wells Fargo Retail - Repairs and Maintenance
                            - setScreenPop:
                                name: Set Screen Pop
                                screenPopScript:
                                  Client Label Script:
                                    inputs:
                                      Client Label:
                                        lit: Wells Fargo Retail - Repairs and Maintenance
                        "no":
                          actions:
                            - setParticipantData:
                                name: Set Participant Data
                                attributes:
                                  - attribute:
                                      name:
                                        lit: Report Group
                                      value:
                                        lit: Wells Fargo Corporate - Repairs and Maintenance
                            - setScreenPop:
                                name: Set Screen Pop
                                screenPopScript:
                                  Client Label Script:
                                    inputs:
                                      Client Label:
                                        lit: Wells Fargo Corporate - Repairs and Maintenance
                  - jumpToTask:
                      name: Jump to Reusable Task
                      targetTaskRef: "/inboundCall/tasks/task[Wells Fargo_18]"
          - menuTask:
              name: Furniture
              dtmf: digit_4
              globalDtmf: false
              globalSpeechRecTerms: false
              task:
                actions:
                  - decision:
                      name: Decision
                      condition:
                        exp: Call.CalledAddressOriginal=="+***********" or Call.CalledAddressOriginal=="+***********"  or Call.CalledAddressOriginal=="+***********"
                      outputs:
                        "yes":
                          actions:
                            - setParticipantData:
                                name: Set Participant Data
                                attributes:
                                  - attribute:
                                      name:
                                        lit: Report Group
                                      value:
                                        lit: Wells Fargo Retail - Furniture
                            - setScreenPop:
                                name: Set Screen Pop
                                screenPopScript:
                                  Client Label Script:
                                    inputs:
                                      Client Label:
                                        lit: Wells Fargo Retail - Furniture
                        "no":
                          actions:
                            - setParticipantData:
                                name: Set Participant Data
                                attributes:
                                  - attribute:
                                      name:
                                        lit: Report Group
                                      value:
                                        lit: Wells Fargo Corporate - Furniture
                            - setScreenPop:
                                name: Set Screen Pop
                                screenPopScript:
                                  Client Label Script:
                                    inputs:
                                      Client Label:
                                        lit: Wells Fargo Corporate - Furniture
                  - jumpToTask:
                      name: Jump to Reusable Task
                      targetTaskRef: "/inboundCall/tasks/task[Wells Fargo_18]"
          - menuTask:
              name: Business Technology Asset Inventory
              dtmf: digit_5
              globalDtmf: false
              globalSpeechRecTerms: false
              task:
                actions:
                  - decision:
                      name: Decision
                      condition:
                        exp: Call.CalledAddressOriginal=="+***********" or Call.CalledAddressOriginal=="+***********"  or Call.CalledAddressOriginal=="+***********"
                      outputs:
                        "yes":
                          actions:
                            - setParticipantData:
                                name: Set Participant Data
                                attributes:
                                  - attribute:
                                      name:
                                        lit: Report Group
                                      value:
                                        lit: Wells Fargo Retail - Business Technology Asset Inventory
                            - setScreenPop:
                                name: Set Screen Pop
                                screenPopScript:
                                  Client Label Script:
                                    inputs:
                                      Client Label:
                                        lit: Wells Fargo Retail - Business Technology Asset Inventory
                        "no":
                          actions:
                            - setParticipantData:
                                name: Set Participant Data
                                attributes:
                                  - attribute:
                                      name:
                                        lit: Report Group
                                      value:
                                        lit: Wells Fargo Corporate - Business Technology Asset Inventory
                            - setScreenPop:
                                name: Set Screen Pop
                                screenPopScript:
                                  Client Label Script:
                                    inputs:
                                      Client Label:
                                        lit: Wells Fargo Corporate - Business Technology Asset Inventory
                  - jumpToTask:
                      name: Jump to Reusable Task
                      targetTaskRef: "/inboundCall/tasks/task[Wells Fargo_18]"
          - menuTask:
              name: Low Priority
              dtmf: digit_9
              globalDtmf: false
              globalSpeechRecTerms: false
              task:
                actions:
                  - updateData:
                      name: Update Data
                      statements:
                        - boolean:
                            variable: Flow.LowPriority
                            value:
                              lit: true
                  - decision:
                      name: Decision
                      condition:
                        exp: Call.CalledAddressOriginal=="+***********" or Call.CalledAddressOriginal=="+***********"  or Call.CalledAddressOriginal=="+***********"
                      outputs:
                        "yes":
                          actions:
                            - setParticipantData:
                                name: Set Participant Data
                                attributes:
                                  - attribute:
                                      name:
                                        lit: Report Group
                                      value:
                                        lit: Wells Fargo Retail - FM Option
                            - setScreenPop:
                                name: Set Screen Pop
                                screenPopScript:
                                  Client Label Script:
                                    inputs:
                                      Client Label:
                                        lit: Wells Fargo Corporate - FM Option
                        "no":
                          actions:
                            - setParticipantData:
                                name: Set Participant Data
                                attributes:
                                  - attribute:
                                      name:
                                        lit: Report Group
                                      value:
                                        lit: Wells Fargo Corporate - FM Option
                            - setScreenPop:
                                name: Set Screen Pop
                                screenPopScript:
                                  Client Label Script:
                                    inputs:
                                      Client Label:
                                        lit: Wells Fargo Corporate - FM Option
                  - jumpToTask:
                      name: Jump to Reusable Task
                      targetTaskRef: "/inboundCall/tasks/task[Wells Fargo_18]"
          - menuJumpToMenu:
              name: Security Devices
              dtmf: digit_3
              globalDtmf: false
              globalSpeechRecTerms: false
              targetMenuRef: "/inboundCall/menus/menu[Security Menu_93]"
        defaultChildMenuRef: "./choices/menuTask[Repairs and Maintenance_37]"
    - menu:
        name: Security Menu
        refId: Security Menu_93
        audio:
          prompt: Prompt.WellsFargoSecurityMenu
        settingsMenu:
          extensionDialingMaxDelay:
            noValue: true
          listenForExtensionDialing:
            noValue: true
          menuSelectionTimeout:
            noValue: true
          repeatCount:
            noValue: true
        settingsSpeechRec:
          completeMatchTimeout:
            noValue: true
          incompleteMatchTimeout:
            noValue: true
          maxSpeechLengthTimeout:
            noValue: true
          minConfidenceLevel:
            noValue: true
        choices:
          - menuTask:
              name: Burglary Alarms
              dtmf: digit_1
              globalDtmf: false
              globalSpeechRecTerms: false
              task:
                variables:
                  - integerVariable:
                      name: Task.NA
                      initialValue:
                        noValue: true
                      isInput: false
                      isOutput: false
                actions:
                  - setParticipantData:
                      name: Set Participant Data
                      attributes:
                        - attribute:
                            name:
                              lit: Report Group
                            value:
                              lit: Wells Fargo - Burglary Alarms
                  - loop:
                      name: Loop
                      currentIndex:
                        var: Task.NA
                      loopCount:
                        lit: 3
                      outputs:
                        loop:
                          actions:
                            - playAudio:
                                name: Play Audio
                                audio:
                                  prompt: Prompt.WellsFargoBurglaryAlarms
                  - transferToNumber:
                      name: Transfer to Number
                      targetNumber:
                        lit: "18774949355"
                      preTransferAudio:
                        exp: AudioPlaybackOptions(ToAudioBlank(100), true)
                      failureTransferAudio:
                        exp: AudioPlaybackOptions(ToAudioBlank(100), true)
                      releaseLinkTransfer:
                        lit: false
                      connectTimeout:
                        noValue: true
                      failureOutputs:
                        errorType:
                          noValue: true
                        errorMessage:
                          noValue: true
                      outputs:
                        failure:
                          actions:
                            - playAudio:
                                name: Play Audio
                                audio:
                                  tts: Transfer failed.
                  - disconnect:
                      name: Disconnect
          - menuTask:
              name: Electronic Security Equipment
              dtmf: digit_2
              globalDtmf: false
              globalSpeechRecTerms: false
              task:
                actions:
                  - setParticipantData:
                      name: Set Participant Data
                      attributes:
                        - attribute:
                            name:
                              lit: Report Group
                            value:
                              lit: Wells Fargo - Electronic Security Equipment
                  - jumpToTask:
                      name: Jump to Reusable Task
                      targetTaskRef: "/inboundCall/tasks/task[All other Security_101]"
          - menuTask:
              name: Security Cameras
              dtmf: digit_3
              globalDtmf: false
              globalSpeechRecTerms: false
              task:
                actions:
                  - setParticipantData:
                      name: Set Participant Data
                      attributes:
                        - attribute:
                            name:
                              lit: Report Group
                            value:
                              lit: Wells Fargo - Security Cameras
                  - jumpToTask:
                      name: Jump to Reusable Task
                      targetTaskRef: "/inboundCall/tasks/task[All other Security_101]"
          - menuTask:
              name: DVR/NVR
              dtmf: digit_4
              globalDtmf: false
              globalSpeechRecTerms: false
              task:
                actions:
                  - setParticipantData:
                      name: Set Participant Data
                      attributes:
                        - attribute:
                            name:
                              lit: Report Group
                            value:
                              lit: Wells Fargo - DVR/NVR
                  - jumpToTask:
                      name: Jump to Reusable Task
                      targetTaskRef: "/inboundCall/tasks/task[All other Security_101]"
          - menuTask:
              name: Electronic Trac Packs
              dtmf: digit_5
              globalDtmf: false
              globalSpeechRecTerms: false
              task:
                actions:
                  - setParticipantData:
                      name: Set Participant Data
                      attributes:
                        - attribute:
                            name:
                              lit: Report Group
                            value:
                              lit: Wells Fargo - Electronic Trac Packs
                  - jumpToTask:
                      name: Jump to Reusable Task
                      targetTaskRef: "/inboundCall/tasks/task[All other Security_101]"
          - menuTransferToNumber:
              name: Default
              refId: Default_133
              dtmf: "digit_#"
              globalDtmf: false
              globalSpeechRecTerms: false
              targetNumber:
                lit: "18779321852"
              preTransferAudio:
                exp: AudioPlaybackOptions(ToAudioBlank(100), true)
              failureTransferAudio:
                exp: AudioPlaybackOptions(ToAudioBlank(100), true)
              releaseLinkTransfer:
                lit: false
              connectTimeout:
                noValue: true
              failureOutputs:
                errorType:
                  noValue: true
                errorMessage:
                  noValue: true
        defaultChildMenuRef: "./choices/menuTransferToNumber[Default_133]"
  tasks:
    - task:
        name: Wells Fargo
        refId: Wells Fargo_18
        actions:
          - evaluateSchedule:
              name: Evaluate Schedule
              evaluationType:
                schedule:
                  selectedSchedule:
                    lit:
                      name: Wells Fargo Emergency Routing
                  name: Wells Fargo Emergency Routing
                  evaluationTimeZone: America/New_York
                  scheduleTimeZone:
                    lit: America/New_York
                  evaluate:
                    now: true
              outputs:
                active:
                  actions:
                    - transferToAcd:
                        name: Transfer to ACD
                        targetQueue:
                          lit:
                            name: NAM - Wells Fargo Operation - English
                        preTransferAudio:
                          exp: AudioPlaybackOptions(ToAudioBlank(100), true)
                        failureTransferAudio:
                          exp: AudioPlaybackOptions(ToAudioBlank(100), true)
                        priority:
                          lit: 0
                        preferredAgents:
                          noValue: true
                        languageSkill:
                          noValue: true
                        directAgent:
                          noValue: true
                        failureOutputs:
                          errorType:
                            noValue: true
                          errorMessage:
                            noValue: true
                inactive:
                  actions:
                    - setParticipantData:
                        name: Set Participant Data
                        attributes:
                          - attribute:
                              name:
                                lit: LowPriority
                              value:
                                exp: ToString(Flow.LowPriority)
                    - decision:
                        name: Decision
                        condition:
                          exp: Flow.LowPriority==true
                        outputs:
                          "yes":
                            actions:
                              - transferToAcd:
                                  name: Transfer to ACD
                                  targetQueue:
                                    lit:
                                      name: Wells Fargo - Wells Fargo
                                  overrideInQueueFlow:
                                    name: Wells Fargo - In-Queue 1
                                  preTransferAudio:
                                    exp: AudioPlaybackOptions(ToAudioBlank(100), true)
                                  failureTransferAudio:
                                    exp: AudioPlaybackOptions(ToAudioBlank(100), true)
                                  priority:
                                    lit: -2
                                  preferredAgents:
                                    noValue: true
                                  languageSkill:
                                    noValue: true
                                  directAgent:
                                    noValue: true
                                  failureOutputs:
                                    errorType:
                                      noValue: true
                                    errorMessage:
                                      noValue: true
                          "no":
                            actions:
                              - transferToAcd:
                                  name: Transfer to ACD
                                  targetQueue:
                                    lit:
                                      name: Wells Fargo - Wells Fargo
                                  overrideInQueueFlow:
                                    name: Wells Fargo - In-Queue 1
                                  preTransferAudio:
                                    exp: AudioPlaybackOptions(ToAudioBlank(100), true)
                                  failureTransferAudio:
                                    exp: AudioPlaybackOptions(ToAudioBlank(100), true)
                                  priority:
                                    lit: 0
                                  preferredAgents:
                                    noValue: true
                                  languageSkill:
                                    noValue: true
                                  directAgent:
                                    noValue: true
                                  failureOutputs:
                                    errorType:
                                      noValue: true
                                    errorMessage:
                                      noValue: true
          - disconnect:
              name: Disconnect
    - task:
        name: Emergency Routing Check
        refId: Emergency Routing Check_81
        actions:
          - evaluateSchedule:
              name: Evaluate Schedule
              evaluationType:
                schedule:
                  selectedSchedule:
                    lit:
                      name: Wells Fargo Emergency Routing
                  name: Wells Fargo Emergency Routing
                  evaluationTimeZone: America/New_York
                  scheduleTimeZone:
                    lit: America/New_York
                  evaluate:
                    now: true
              outputs:
                active:
                  actions:
                    - decision:
                        name: Decision
                        condition:
                          exp: Call.CalledAddressOriginal=="+***********"
                        outputs:
                          "yes":
                            actions:
                              - transferToNumber:
                                  name: Transfer to Number
                                  targetNumber:
                                    lit: "+***********"
                                  preTransferAudio:
                                    exp: AudioPlaybackOptions(ToAudioBlank(100), true)
                                  failureTransferAudio:
                                    exp: AudioPlaybackOptions(ToAudioBlank(100), true)
                                  releaseLinkTransfer:
                                    lit: false
                                  connectTimeout:
                                    noValue: true
                                  failureOutputs:
                                    errorType:
                                      noValue: true
                                    errorMessage:
                                      noValue: true
                          "no":
                            actions:
                              - transferToNumber:
                                  name: Transfer to Number
                                  targetNumber:
                                    lit: "+14122086712"
                                  preTransferAudio:
                                    exp: AudioPlaybackOptions(ToAudioBlank(100), true)
                                  failureTransferAudio:
                                    exp: AudioPlaybackOptions(ToAudioBlank(100), true)
                                  releaseLinkTransfer:
                                    lit: false
                                  connectTimeout:
                                    noValue: true
                                  failureOutputs:
                                    errorType:
                                      noValue: true
                                    errorMessage:
                                      noValue: true
          - playAudio:
              name: Play Audio
              audio:
                prompt: Prompt.WellsFargoGreeting
          - jumpToMenu:
              name: Jump to Menu
              targetMenuRef: "/inboundCall/menus/menu[Main Menu_10]"
    - task:
        name: All other Security
        refId: All other Security_101
        variables:
          - integerVariable:
              name: Task.NA
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
        actions:
          - loop:
              name: Loop
              currentIndex:
                var: Task.NA
              loopCount:
                lit: 3
              outputs:
                loop:
                  actions:
                    - playAudio:
                        name: Play Audio
                        audio:
                          prompt: Prompt.WellsFargoOtherSecurity
          - transferToNumber:
              name: Transfer to Number
              targetNumber:
                lit: "18779321852"
              preTransferAudio:
                exp: AudioPlaybackOptions(ToAudioBlank(100), true)
              failureTransferAudio:
                exp: AudioPlaybackOptions(ToAudioBlank(100), true)
              releaseLinkTransfer:
                lit: false
              connectTimeout:
                noValue: true
              failureOutputs:
                errorType:
                  noValue: true
                errorMessage:
                  noValue: true
              outputs:
                failure:
                  actions:
                    - playAudio:
                        name: Play Audio
                        audio:
                          tts: Transfer Failed
          - disconnect:
              name: Disconnect
